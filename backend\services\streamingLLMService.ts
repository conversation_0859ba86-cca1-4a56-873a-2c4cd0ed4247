// 🔥 流式传输优化的LLM服务 - 专注于极致速度
import OpenAI from 'openai';
import { logger } from '../utils/logger.js';
import { RedisService } from './redisService.js';
import {
  OptimizedPrompts,
  getOptimizedSystemPrompt,
  generateCacheKey,
  getPerformanceConfig
} from '../config/optimizedPrompts.js';
import crypto from 'crypto';

interface StreamingLLMConfig {
  provider: 'deepseek';
  apiKey?: string;
  baseURL: string;
  model: string;
  timeout: number;
  enableCaching: boolean;
  cacheExpiry: number; // 缓存过期时间（秒）
}

interface InterviewContext {
  companyName: string;
  positionName: string;
  candidateBackground?: string;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  previousQuestions: string[];
  previousAnswers: string[];
  currentQuestionIndex: number;
  totalQuestions: number;
}

interface StreamingCallbacks {
  onChunk?: (token: string) => void;
  onEnd?: () => void;
  onError?: (error: Error) => void;
}

export class StreamingLLMService {
  private deepseekClient: OpenAI | null = null;
  private config: StreamingLLMConfig;
  private redis: RedisService;

  constructor() {
    // 🔥 专注于DeepSeek V3的优化配置
    this.config = {
      provider: 'deepseek',
      apiKey: process.env.DEEPSEEK_API_KEY || '***********************************',
      model: 'deepseek-chat', // 专注于V3模型
      baseURL: 'https://api.deepseek.com',
      timeout: 15000, // 🔥 减少超时时间到15秒
      enableCaching: true,
      cacheExpiry: 3600 // 1小时缓存
    };

    this.redis = RedisService.getInstance();
    this.initializeClient();
    logger.info('🚀 StreamingLLMService initialized with DeepSeek V3 optimization');
  }

  /**
   * 初始化DeepSeek客户端
   */
  private initializeClient(): void {
    try {
      if (this.config.apiKey) {
        this.deepseekClient = new OpenAI({
          apiKey: this.config.apiKey,
          baseURL: this.config.baseURL,
          timeout: this.config.timeout
        });
        logger.info('🤖 DeepSeek V3 client initialized for streaming');
      } else {
        logger.error('❌ DeepSeek API key not configured');
      }
    } catch (error) {
      logger.error('❌ Failed to initialize DeepSeek client:', error);
    }
  }

  /**
   * 🔥 生成缓存键 - 使用优化的配置
   */
  private generateCacheKey(context: InterviewContext): string {
    return generateCacheKey('question', {
      position: context.positionName.replace(/\s+/g, '_'),
      index: context.currentQuestionIndex.toString(),
      lang: context.interviewLanguage
    });
  }

  /**
   * 🔥 检查L1缓存（Redis问答缓存）
   */
  private async checkL1Cache(cacheKey: string): Promise<string | null> {
    if (!this.config.enableCaching) return null;
    
    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        logger.info('🎯 L1 Cache HIT - 直接返回缓存答案');
        return cached;
      }
      return null;
    } catch (error) {
      logger.warn('⚠️ L1 Cache check failed:', error);
      return null;
    }
  }

  /**
   * 🔥 保存到L1缓存
   */
  private async saveToL1Cache(cacheKey: string, content: string): Promise<void> {
    if (!this.config.enableCaching) return;
    
    try {
      await this.redis.set(cacheKey, content, this.config.cacheExpiry);
      logger.info('💾 L1 Cache SAVE - 答案已缓存');
    } catch (error) {
      logger.warn('⚠️ L1 Cache save failed:', error);
    }
  }

  /**
   * 🔥 优化的系统提示词 - 使用配置文件
   */
  private getSystemPrompt(language: 'chinese' | 'english'): string {
    return getOptimizedSystemPrompt('interviewer', language);
  }

  /**
   * 🔥 流式生成面试问题 - 核心优化方法
   */
  async generateQuestionStream(
    context: InterviewContext,
    callbacks: StreamingCallbacks
  ): Promise<string> {
    const cacheKey = this.generateCacheKey(context);

    // 🔥 L1缓存检查
    const cachedAnswer = await this.checkL1Cache(cacheKey);
    if (cachedAnswer) {
      // 模拟流式输出缓存内容
      if (callbacks.onChunk) {
        const words = cachedAnswer.split('');
        for (const char of words) {
          callbacks.onChunk(char);
          await new Promise(resolve => setTimeout(resolve, 10)); // 10ms间隔模拟打字效果
        }
      }
      if (callbacks.onEnd) callbacks.onEnd();
      return cachedAnswer;
    }

    // 🔥 L1未命中，调用API（利用L2上下文缓存）
    return this.callStreamingAPI(context, cacheKey, callbacks);
  }

  /**
   * 🔥 调用流式API - 利用DeepSeek L2上下文缓存
   */
  private async callStreamingAPI(
    context: InterviewContext,
    cacheKey: string,
    callbacks: StreamingCallbacks
  ): Promise<string> {
    if (!this.deepseekClient) {
      throw new Error('DeepSeek client not initialized');
    }

    try {
      const startTime = Date.now();
      logger.info('🤖 Calling DeepSeek V3 streaming API...');

      // 🔥 构建包含历史的messages以最大化L2缓存命中
      const messages = this.buildMessagesWithHistory(context);

      // 🔥 获取性能优化配置
      const perfConfig = getPerformanceConfig('question');

      const stream = await this.deepseekClient.chat.completions.create({
        model: this.config.model,
        messages,
        temperature: perfConfig.temperature,
        max_tokens: perfConfig.maxTokens,
        stream: true // 🔥 启用流式传输
      });

      let accumulatedContent = '';
      let firstTokenTime: number | null = null;

      // 🔥 处理流式响应
      for await (const chunk of stream) {
        const content = chunk.choices?.[0]?.delta?.content || '';
        if (content) {
          if (!firstTokenTime) {
            firstTokenTime = Date.now();
            logger.info(`⚡ First token received in ${firstTokenTime - startTime}ms`);
          }
          
          accumulatedContent += content;
          if (callbacks.onChunk) {
            callbacks.onChunk(content);
          }
        }
      }

      const totalTime = Date.now() - startTime;
      logger.info(`✅ DeepSeek V3 streaming completed in ${totalTime}ms`);

      // 🔥 保存到L1缓存
      await this.saveToL1Cache(cacheKey, accumulatedContent);

      if (callbacks.onEnd) callbacks.onEnd();
      return accumulatedContent;

    } catch (error) {
      logger.error('❌ Streaming API call failed:', error);
      if (callbacks.onError) callbacks.onError(error as Error);
      throw error;
    }
  }

  /**
   * 🔥 构建优化的提示词
   */
  private buildOptimizedPrompt(context: InterviewContext): string {
    const language = context.interviewLanguage === 'chinese' ? '中文' : 'English';
    
    return `面试设置：${context.companyName} ${context.positionName}岗位
当前进度：第${context.currentQuestionIndex + 1}/${context.totalQuestions}题
语言：${language}

请生成一个简洁的面试问题（不超过30字）。`;
  }

  /**
   * 🔥 构建包含历史的消息数组 - 最大化L2上下文缓存命中率
   */
  private buildMessagesWithHistory(context: InterviewContext): any[] {
    const messages = [
      {
        role: 'system',
        content: this.getSystemPrompt(context.interviewLanguage)
      }
    ];

    // 🔥 添加历史对话以利用L2缓存
    for (let i = 0; i < Math.min(context.previousQuestions.length, 3); i++) {
      messages.push({
        role: 'assistant',
        content: context.previousQuestions[i]
      });

      if (context.previousAnswers[i]) {
        messages.push({
          role: 'user',
          content: context.previousAnswers[i]
        });
      }
    }

    // 添加当前提示
    const prompt = this.buildOptimizedPrompt(context);
    messages.push({
      role: 'user',
      content: prompt
    });

    return messages;
  }

  /**
   * 🔥 指数退避重试机制
   */
  async callWithRetry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        // 检查是否为不可重试的错误
        if (this.isNonRetryableError(error)) {
          throw error;
        }
        
        if (attempt < maxRetries) {
          const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000; // 添加抖动
          logger.warn(`⚠️ Attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * 🔥 判断是否为不可重试的错误
   */
  private isNonRetryableError(error: any): boolean {
    const nonRetryableCodes = [400, 401, 402, 422];
    return nonRetryableCodes.includes(error?.status) || 
           nonRetryableCodes.includes(error?.response?.status);
  }
}
