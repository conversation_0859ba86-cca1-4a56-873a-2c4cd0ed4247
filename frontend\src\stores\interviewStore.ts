import { create } from 'zustand';

export type ScreenShareStatus = 'idle' | 'pending' | 'sharing' | 'denied';

export interface InterviewConfig {
  selectedPositionId: string | null;
  interviewLanguage: 'chinese' | 'english';
  answerStyle: 'keywords_conversational' | 'conversational';
  secondaryScreen: boolean;
  audioCollection: boolean;
  screenShareStatus: ScreenShareStatus;
  sharedStream: MediaStream | null;
}

interface InterviewState {
  config: InterviewConfig;
  setSelectedPosition: (positionId: string | null) => void;
  setInterviewLanguage: (language: 'chinese' | 'english') => void;
  setAnswerStyle: (style: 'keywords_conversational' | 'conversational') => void;
  setSecondaryScreen: (enabled: boolean) => void;
  setAudioCollection: (enabled: boolean) => void;
  setScreenShareStatus: (status: ScreenShareStatus) => void;
  setSharedStream: (stream: MediaStream | null) => void;
  resetConfig: () => void;
}

const defaultConfig: InterviewConfig = {
  selectedPositionId: null,
  interviewLanguage: 'chinese',
  answerStyle: 'keywords_conversational',
  secondaryScreen: false,
  audioCollection: false,
  screenShareStatus: 'idle',
  sharedStream: null,
};

const useInterviewStore = create<InterviewState>((set, get) => ({
  config: defaultConfig,
  setSelectedPosition: (positionId) =>
    set((state) => ({
      config: { ...state.config, selectedPositionId: positionId }
    })),
  setInterviewLanguage: (language) =>
    set((state) => ({
      config: { ...state.config, interviewLanguage: language }
    })),
  setAnswerStyle: (style) =>
    set((state) => ({
      config: { ...state.config, answerStyle: style }
    })),
  setSecondaryScreen: (enabled) =>
    set((state) => ({
      config: { ...state.config, secondaryScreen: enabled }
    })),
  setAudioCollection: (enabled) =>
    set((state) => ({
      config: { ...state.config, audioCollection: enabled }
    })),
  setScreenShareStatus: (status) =>
    set((state) => ({
      config: { ...state.config, screenShareStatus: status }
    })),
  setSharedStream: (stream) => {
    const oldStream = get().config.sharedStream;
    if (oldStream && oldStream !== stream) {
      oldStream.getTracks().forEach(track => track.stop());
      console.log('Old shared stream stopped in store setter.');
    }
    set((state) => ({
      config: {
        ...state.config,
        sharedStream: stream,
        audioCollection: !!(stream && stream.getAudioTracks().length > 0)
      }
    }));
  },
  resetConfig: () => {
    const currentStream = get().config.sharedStream;
    if (currentStream) {
      currentStream.getTracks().forEach(track => track.stop());
      console.log('Shared stream stopped on resetConfig.');
    }
    set({ config: { ...defaultConfig, sharedStream: null } });
  }
}));

export default useInterviewStore;
