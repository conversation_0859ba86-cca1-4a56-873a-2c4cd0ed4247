// WebSocket消息处理器
import { AuthenticatedWebSocket, IncomingMessage, AudioChunkMessage, ClientLogMessage, MockInterviewAnswerMessage } from '../../types/websocket.js';
import { SessionManager } from './sessionManager.js';
import { AudioProcessor } from '../providers/audio/audioProcessor.js';
import { AISuggestionService } from './aiSuggestionService.js';
import { MockInterviewService } from './mockInterviewService.js';
import { logger, clientLogger } from '../../utils/logger.js';
import prisma from '../../lib/prisma.js';

export class MessageHandler {
  private sessionManager: SessionManager;
  private audioProcessor: AudioProcessor;
  private aiSuggestionService: AISuggestionService;
  private mockInterviewService: MockInterviewService;

  constructor(sessionManager: SessionManager, audioProcessor: AudioProcessor | null, aiSuggestionService: AISuggestionService | null, mockInterviewService: MockInterviewService) {
    this.sessionManager = sessionManager;
    this.audioProcessor = audioProcessor as AudioProcessor;
    this.aiSuggestionService = aiSuggestionService as AISuggestionService;
    this.mockInterviewService = mockInterviewService;

    // 监听音频处理器事件（如果存在）
    if (this.audioProcessor) {
      this.setupAudioProcessorEvents();
    }
  }

  /**
   * 🔥 动态更新音频服务（用于延迟初始化）
   */
  updateAudioServices(audioProcessor: AudioProcessor, aiSuggestionService: AISuggestionService): void {
    this.audioProcessor = audioProcessor;
    this.aiSuggestionService = aiSuggestionService;

    // 设置事件监听
    this.setupAudioProcessorEvents();

    console.log('✅ MessageHandler audio services updated');
  }

  /**
   * 设置音频处理器事件监听
   */
  private setupAudioProcessorEvents(): void {
    if (!this.audioProcessor) {
      console.log('⚠️ AudioProcessor not available, skipping event setup');
      return;
    }

    // 🔥 监听实时转录部分结果
    this.audioProcessor.on('transcription_partial', (data) => {
      this.handleTranscriptionPartial(data);
    });

    // 🔥 监听实时转录最终结果
    this.audioProcessor.on('transcription_final', (data) => {
      this.handleTranscriptionFinal(data);
    });

    // 监听转录结果（兼容性）
    this.audioProcessor.on('transcription_result', (data) => {
      this.handleTranscriptionResult(data);
    });

    // 监听完整转录（兼容性）
    this.audioProcessor.on('complete_transcription', (data) => {
      this.handleCompleteTranscription(data);
    });

    // 监听LLM触发
    this.audioProcessor.on('llm_trigger', (data) => {
      this.handleLLMTrigger(data);
    });

    // 监听ASR错误
    this.audioProcessor.on('asr_error', (data) => {
      this.handleASRError(data);
    });
  }

  /**
   * 处理传入的WebSocket消息
   */
  async handleMessage(ws: AuthenticatedWebSocket, message: Buffer | string): Promise<void> {
    const sessionId = this.sessionManager.getClientSession(ws);
    if (!sessionId) {
      console.error('Received message from WebSocket without session mapping');
      return;
    }

    console.log(`[${sessionId}] Received message from client`);
    console.log(`[${sessionId}] Message type: ${typeof message}, length: ${message.length}`);

    try {
      if (message instanceof Buffer) {
        await this.handleBufferMessage(ws, sessionId, message);
      } else {
        await this.handleStringMessage(ws, sessionId, message as string);
      }
    } catch (error) {
      console.error(`[${sessionId}] Error handling message:`, error);
      this.sendErrorMessage(ws, 'Message processing failed', error);
    }
  }

  /**
   * 处理Buffer类型消息
   */
  private async handleBufferMessage(ws: AuthenticatedWebSocket, sessionId: string, message: Buffer): Promise<void> {
    try {
      // 尝试解析为JSON
      const messageStr = message.toString('utf8');
      const parsedMessage = JSON.parse(messageStr);
      console.log(`[${sessionId}] Successfully parsed Buffer as JSON message:`, parsedMessage.type);
      
      await this.handleParsedMessage(ws, sessionId, parsedMessage);
    } catch (parseError) {
      // 如果不是JSON，当作原始音频数据处理
      console.log(`[${sessionId}] Received raw audio data, size: ${message.length} bytes`);
      await this.handleRawAudioData(ws, sessionId, message);
    }
  }

  /**
   * 处理字符串类型消息
   */
  private async handleStringMessage(ws: AuthenticatedWebSocket, sessionId: string, message: string): Promise<void> {
    try {
      const parsedMessage = JSON.parse(message);
      console.log(`[${sessionId}] Received JSON message:`, parsedMessage.type);
      
      await this.handleParsedMessage(ws, sessionId, parsedMessage);
    } catch (parseError) {
      // 如果不是JSON，记录但不处理（移除echo功能，避免干扰AI正式面试）
      console.log(`[${sessionId}] Received non-JSON message from user ${ws.userId}: ${message}`);
      console.log(`[${sessionId}] Non-JSON messages are ignored in live interview mode`);
    }
  }

  /**
   * 处理解析后的JSON消息
   */
  private async handleParsedMessage(ws: AuthenticatedWebSocket, sessionId: string, parsedMessage: IncomingMessage): Promise<void> {
    console.log(`[${sessionId}] 🔍 Processing message type: ${parsedMessage.type}`);

    switch (parsedMessage.type) {
      case 'client_log':
        this.handleClientLog(parsedMessage as ClientLogMessage);
        break;

      case 'smart_audio_segment':
      case 'pcm_audio_chunk':
      case 'audio_chunk':
        await this.handleAudioChunk(ws, sessionId, parsedMessage as AudioChunkMessage);
        break;

      case 'start_mock_interview':
        console.log(`[${sessionId}] 🎯 Handling start_mock_interview message`);
        await this.handleStartMockInterview(ws, sessionId, parsedMessage);
        break;

      case 'mock_interview_answer':
        await this.handleMockInterviewAnswer(ws, sessionId, parsedMessage as MockInterviewAnswerMessage);
        break;

      default:
        console.log(`[${sessionId}] ❓ Unhandled message type: ${parsedMessage.type}`);
        break;
    }
  }

  /**
   * 处理客户端日志
   */
  private handleClientLog(message: ClientLogMessage): void {
    const level = message.level || 'info';
    const msg = message.message;
    
    const loggerAny = clientLogger as any;
    if (typeof loggerAny[level] === 'function') {
      loggerAny[level](msg);
    } else {
      clientLogger.info(msg);
    }
  }

  /**
   * 处理音频块消息
   */
  private async handleAudioChunk(ws: AuthenticatedWebSocket, sessionId: string, message: AudioChunkMessage): Promise<void> {
    try {
      console.log(`[${sessionId}] 收到音频块: ${message.format}, 哈希: ${message.audioHash?.substring(0, 8)}...`);
      
      // 解码base64音频数据
      const audioBuffer = Buffer.from(message.payload, 'base64');
      
      // 根据消息类型处理音频，传递哈希用于重复检测
      switch (message.type) {
        case 'audio_chunk':
          await this.audioProcessor.processAudioChunk(sessionId, audioBuffer, message.format || 'webm');
          break;
        case 'pcm_audio_chunk':
          // 将Buffer转换为Float32Array
          const float32Array = new Float32Array(audioBuffer.buffer, audioBuffer.byteOffset, audioBuffer.byteLength / 4);
          await this.audioProcessor.processPCMAudioChunk(sessionId, float32Array, message.audioHash);
          break;
        case 'smart_audio_segment':
          await this.audioProcessor.processSmartAudioSegment(sessionId, audioBuffer, message.segmentInfo);
          break;
      }
      
    } catch (error) {
      console.error(`[${sessionId}] 音频块处理错误:`, error);
      this.sendErrorMessage(ws, 'Audio processing failed', error);
    }
  }

  /**
   * 处理原始音频数据
   */
  private async handleRawAudioData(ws: AuthenticatedWebSocket, sessionId: string, audioData: Buffer): Promise<void> {
    // 🔥 检查音频处理器是否可用
    if (!this.audioProcessor) {
      console.log(`[${sessionId}] Audio processor not available, ignoring audio data`);
      return;
    }

    try {
      await this.audioProcessor.processRawAudioData(sessionId, audioData);
    } catch (error) {
      console.error(`[${sessionId}] Error processing raw audio data:`, error);
      this.sendErrorMessage(ws, 'Raw audio processing failed', error);
    }
  }

  /**
   * 处理转录结果事件
   */
  private handleTranscriptionResult(data: any): void {
    const { sessionId, text, speaker, confidence, timestamp, service, isPartial } = data;
    console.log(`📤 Broadcasting transcription result for session ${sessionId}: "${text}"`);

    const message = JSON.stringify({
      type: 'transcription',
      text,
      speaker,
      confidence,
      timestamp,
      service,
      isPartial
    });

    this.sessionManager.broadcastToSession(sessionId, message);
  }

  /**
   * 🔥 处理实时转录部分结果
   */
  private handleTranscriptionPartial(data: any): void {
    const { sessionId, text, bubbleId, timestamp } = data;
    console.log(`📝 Broadcasting partial transcription for session ${sessionId}, bubble ${bubbleId}: "${text}"`);

    const message = JSON.stringify({
      type: 'transcription_partial',
      text,
      bubbleId,
      timestamp,
      service: 'dashscope'
    });

    this.sessionManager.broadcastToSession(sessionId, message);
  }

  /**
   * 🔥 处理实时转录最终结果
   */
  private handleTranscriptionFinal(data: any): void {
    const { sessionId, text, bubbleId, timestamp } = data;
    console.log(`🎯 Broadcasting final transcription for session ${sessionId}, bubble ${bubbleId}: "${text}"`);

    const message = JSON.stringify({
      type: 'transcription_final',
      text,
      bubbleId,
      timestamp,
      service: 'dashscope'
    });

    this.sessionManager.broadcastToSession(sessionId, message);
  }

  /**
   * 处理完整转录事件（兼容性）
   */
  private handleCompleteTranscription(data: any): void {
    const { sessionId, text, speaker, timestamp } = data;
    console.log(`📤 Broadcasting complete transcription for session ${sessionId}: "${text}"`);

    const message = JSON.stringify({
      type: 'transcription',
      text,
      speaker,
      timestamp
    });

    this.sessionManager.broadcastToSession(sessionId, message);
  }

  /**
   * 🔥 处理LLM触发事件 - 优化版本，支持岗位类型推断
   */
  private async handleLLMTrigger(data: any): Promise<void> {
    const { sessionId, text } = data;
    console.log(`🤖 Triggering AI suggestion for session ${sessionId}`);

    // 获取会话的第一个WebSocket连接用于AI建议
    const clients = this.sessionManager.getSessionClients(sessionId);
    if (clients && clients.size > 0) {
      const ws = Array.from(clients)[0];

      // 🔥 从数据库获取会话的岗位信息
      let positionName: string | undefined;
      try {
        const session = await prisma.interviewSession.findUnique({
          where: { id: sessionId },
          select: { positionName: true }
        });
        positionName = session?.positionName || undefined;
        console.log(`🎯 Position name for session ${sessionId}:`, positionName);
      } catch (error) {
        console.warn(`⚠️ Failed to get position name for session ${sessionId}:`, error);
      }

      // 调用AI建议服务，传递岗位名称
      this.aiSuggestionService.generateSuggestion(text, sessionId, ws, positionName);
    } else {
      console.warn(`⚠️ No clients found for session ${sessionId} to send AI suggestion`);
    }
  }

  /**
   * 处理ASR错误事件
   */
  private handleASRError(data: any): void {
    const { sessionId, error } = data;
    console.error(`❌ ASR error for session ${sessionId}:`, error);

    const errorMessage = JSON.stringify({
      type: 'asr_error',
      message: 'Speech recognition failed',
      error: error?.message || 'Unknown ASR error',
      timestamp: Date.now()
    });

    this.sessionManager.broadcastToSession(sessionId, errorMessage);
  }

  /**
   * 发送错误消息
   */
  private sendErrorMessage(ws: AuthenticatedWebSocket, message: string, error: any): void {
    const errorMessage = {
      type: 'error',
      message,
      error: error?.message || 'Unknown error',
      timestamp: Date.now()
    };

    if (ws.readyState === 1) { // WebSocket.OPEN
      ws.send(JSON.stringify(errorMessage));
    }
  }

  /**
   * 处理开始AI模拟面试 - 重构版本（先扣费，后启动）
   */
  private async handleStartMockInterview(ws: AuthenticatedWebSocket, sessionId: string, message: any): Promise<void> {
    try {
      console.log(`[${sessionId}] Received start mock interview message`);

      // 检查是否为模拟面试模式
      const interviewMode = (ws as any).interviewMode;
      if (interviewMode !== 'mock') {
        console.warn(`[${sessionId}] Received start mock interview but session is not in mock mode`);
        return;
      }

      // 验证消息格式
      if (!message.config || !message.config.companyName || !message.config.positionName) {
        console.error(`[${sessionId}] Invalid start mock interview message format`);
        this.sendErrorMessage(ws, 'Invalid message format', new Error('Missing required config fields'));
        return;
      }

      // 🔥 阶段1：同步执行扣费验证（必须成功才能继续）
      console.log(`[${sessionId}] 💰 Performing synchronous credit deduction`);

      try {
        // 调用扣费API - 这是同步的，必须成功
        const deductionResult = await this.performCreditDeduction(ws.userId, 'mock');

        if (!deductionResult.success) {
          console.error(`[${sessionId}] Credit deduction failed: ${deductionResult.message}`);
          this.sendErrorMessage(ws, deductionResult.message, new Error('Insufficient credits'));
          return;
        }

        console.log(`[${sessionId}] ✅ Credit deduction successful, proceeding with interview`);
      } catch (deductionError) {
        console.error(`[${sessionId}] Credit deduction error:`, deductionError);
        this.sendErrorMessage(ws, '余额验证失败，请稍后重试', deductionError);
        return;
      }

      // 🔥 阶段2：扣费成功后，启动面试（现在是异步的）
      await this.mockInterviewService.startMockInterview(sessionId, ws.userId, ws, message.config);

    } catch (error) {
      console.error(`[${sessionId}] Error handling start mock interview:`, error);
      this.sendErrorMessage(ws, 'Failed to start mock interview', error);
    }
  }

  /**
   * 🔥 新方法：执行积分扣费
   */
  private async performCreditDeduction(userId: string, type: 'mock' | 'formal'): Promise<{ success: boolean; message: string; newBalance?: any }> {
    try {
      // 动态导入扣费函数以避免循环依赖
      const { deductCreditsAndCreateRecord } = await import('../../routes/usage-records.js');

      const result = await deductCreditsAndCreateRecord(
        userId,
        type,
        `进入AI${type === 'mock' ? '模拟' : '正式'}面试`
      );

      return result;
    } catch (error) {
      console.error('Credit deduction failed:', error);
      return {
        success: false,
        message: '扣费失败，请稍后重试'
      };
    }
  }

  /**
   * 处理AI模拟面试回答
   */
  private async handleMockInterviewAnswer(ws: AuthenticatedWebSocket, sessionId: string, message: MockInterviewAnswerMessage): Promise<void> {
    try {
      console.log(`[${sessionId}] Received mock interview answer for question: ${message.questionId}`);

      // 检查是否为模拟面试模式
      const interviewMode = (ws as any).interviewMode;
      if (interviewMode !== 'mock') {
        console.warn(`[${sessionId}] Received mock interview answer but session is not in mock mode`);
        return;
      }

      // 转发给MockInterviewService处理
      await this.mockInterviewService.handleUserAnswer(sessionId, message, ws);

    } catch (error) {
      console.error(`[${sessionId}] Error handling mock interview answer:`, error);
      this.sendErrorMessage(ws, 'Failed to process mock interview answer', error);
    }
  }

  /**
   * 清理会话
   */
  private cleanupSession(sessionId: string): void {
    this.audioProcessor.cleanup(sessionId);
  }
}
