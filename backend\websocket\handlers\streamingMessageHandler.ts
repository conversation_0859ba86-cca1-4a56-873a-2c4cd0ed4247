// 🔥 流式传输优化的WebSocket消息处理器
import { AuthenticatedWebSocket } from '../types/websocket.js';
import { StreamingLLMService } from '../../services/streamingLLMService.js';
import { logger } from '../../utils/logger.js';

interface StreamingMessage {
  type: 'llm_answer_chunk' | 'llm_stream_start' | 'llm_stream_end' | 'llm_stream_error';
  sessionId: string;
  content?: string;
  error?: string;
  metadata?: {
    questionId?: string;
    isFirstToken?: boolean;
    totalTime?: number;
  };
  timestamp: number;
}

export class StreamingMessageHandler {
  private streamingLLM: StreamingLLMService;
  private activeStreams: Map<string, boolean> = new Map();

  constructor() {
    this.streamingLLM = new StreamingLLMService();
    logger.info('🚀 StreamingMessageHandler initialized');
  }

  /**
   * 🔥 处理流式面试问题生成请求
   */
  async handleStreamingQuestionRequest(
    ws: AuthenticatedWebSocket,
    sessionId: string,
    context: {
      companyName: string;
      positionName: string;
      interviewLanguage: 'chinese' | 'english';
      answerStyle: 'keywords_conversational' | 'conversational';
      previousQuestions?: string[];
      previousAnswers?: string[];
      currentQuestionIndex: number;
      totalQuestions: number;
    }
  ): Promise<void> {
    // 🔥 第一个问题不应该使用流式传输
    if (context.currentQuestionIndex === 0) {
      logger.warn(`⚠️ First question should not use streaming for session ${sessionId}`);
      return;
    }

    // 检查是否已有活跃的流
    if (this.activeStreams.get(sessionId)) {
      logger.warn(`⚠️ Stream already active for session ${sessionId}`);
      return;
    }

    this.activeStreams.set(sessionId, true);
    const questionId = `q-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

    try {
      // 🔥 发送流开始消息
      this.sendStreamMessage(ws, {
        type: 'llm_stream_start',
        sessionId,
        metadata: { questionId, isFirstToken: true },
        timestamp: Date.now()
      });

      const startTime = Date.now();
      let isFirstToken = true;

      // 🔥 调用流式LLM服务
      const question = await this.streamingLLM.generateQuestionStream(
        {
          companyName: context.companyName,
          positionName: context.positionName,
          interviewLanguage: context.interviewLanguage,
          answerStyle: context.answerStyle,
          previousQuestions: context.previousQuestions || [],
          previousAnswers: context.previousAnswers || [],
          currentQuestionIndex: context.currentQuestionIndex,
          totalQuestions: context.totalQuestions
        },
        {
          // 🔥 实时发送内容块
          onChunk: (token: string) => {
            this.sendStreamMessage(ws, {
              type: 'llm_answer_chunk',
              sessionId,
              content: token,
              metadata: { 
                questionId,
                isFirstToken: isFirstToken
              },
              timestamp: Date.now()
            });
            isFirstToken = false;
          },
          
          // 🔥 流结束处理
          onEnd: () => {
            const totalTime = Date.now() - startTime;
            this.sendStreamMessage(ws, {
              type: 'llm_stream_end',
              sessionId,
              metadata: { 
                questionId,
                totalTime
              },
              timestamp: Date.now()
            });
            
            this.activeStreams.delete(sessionId);
            logger.info(`✅ Streaming completed for session ${sessionId} in ${totalTime}ms`);
          },
          
          // 🔥 错误处理
          onError: (error: Error) => {
            this.sendStreamMessage(ws, {
              type: 'llm_stream_error',
              sessionId,
              error: error.message,
              metadata: { questionId },
              timestamp: Date.now()
            });
            
            this.activeStreams.delete(sessionId);
            logger.error(`❌ Streaming failed for session ${sessionId}:`, error);
          }
        }
      );

      logger.info(`🎯 Question generated for session ${sessionId}: ${question.substring(0, 50)}...`);

    } catch (error) {
      logger.error(`❌ Failed to handle streaming request for session ${sessionId}:`, error);
      
      this.sendStreamMessage(ws, {
        type: 'llm_stream_error',
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: { questionId },
        timestamp: Date.now()
      });
      
      this.activeStreams.delete(sessionId);
    }
  }

  /**
   * 🔥 处理流式答案评估请求
   */
  async handleStreamingEvaluationRequest(
    ws: AuthenticatedWebSocket,
    sessionId: string,
    data: {
      questionId: string;
      questionText: string;
      answerText: string;
      context: any;
      expectedKeywords: string[];
    }
  ): Promise<void> {
    if (this.activeStreams.get(sessionId)) {
      logger.warn(`⚠️ Stream already active for session ${sessionId}`);
      return;
    }

    this.activeStreams.set(sessionId, true);
    const evaluationId = `eval-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

    try {
      // 🔥 发送评估流开始消息
      this.sendStreamMessage(ws, {
        type: 'llm_stream_start',
        sessionId,
        metadata: { 
          questionId: data.questionId,
          evaluationId,
          isFirstToken: true 
        },
        timestamp: Date.now()
      });

      const startTime = Date.now();
      let isFirstToken = true;

      // 🔥 这里可以调用流式评估方法（如果需要的话）
      // 目前先发送一个简单的评估完成消息
      setTimeout(() => {
        this.sendStreamMessage(ws, {
          type: 'llm_stream_end',
          sessionId,
          metadata: { 
            questionId: data.questionId,
            evaluationId,
            totalTime: Date.now() - startTime
          },
          timestamp: Date.now()
        });
        
        this.activeStreams.delete(sessionId);
      }, 100);

    } catch (error) {
      logger.error(`❌ Failed to handle streaming evaluation for session ${sessionId}:`, error);
      
      this.sendStreamMessage(ws, {
        type: 'llm_stream_error',
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: { 
          questionId: data.questionId,
          evaluationId 
        },
        timestamp: Date.now()
      });
      
      this.activeStreams.delete(sessionId);
    }
  }

  /**
   * 🔥 发送流式消息到WebSocket
   */
  private sendStreamMessage(ws: AuthenticatedWebSocket, message: StreamingMessage): void {
    try {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify(message));
        
        // 记录首个token的特殊日志
        if (message.metadata?.isFirstToken && message.type === 'llm_answer_chunk') {
          logger.info(`⚡ First token sent for session ${message.sessionId}`);
        }
      } else {
        logger.warn(`⚠️ WebSocket not open for session ${message.sessionId}, state: ${ws.readyState}`);
      }
    } catch (error) {
      logger.error(`❌ Failed to send streaming message for session ${message.sessionId}:`, error);
    }
  }

  /**
   * 🔥 停止指定会话的流
   */
  stopStream(sessionId: string): void {
    if (this.activeStreams.has(sessionId)) {
      this.activeStreams.delete(sessionId);
      logger.info(`🛑 Stream stopped for session ${sessionId}`);
    }
  }

  /**
   * 🔥 获取活跃流的数量
   */
  getActiveStreamCount(): number {
    return this.activeStreams.size;
  }

  /**
   * 🔥 检查指定会话是否有活跃的流
   */
  isStreamActive(sessionId: string): boolean {
    return this.activeStreams.get(sessionId) || false;
  }

  /**
   * 🔥 清理所有活跃的流（用于服务关闭时）
   */
  cleanup(): void {
    const activeCount = this.activeStreams.size;
    this.activeStreams.clear();
    logger.info(`🧹 Cleaned up ${activeCount} active streams`);
  }
}
