// 🔥 优化的提示词配置 - 专注于速度和效率
export const OptimizedPrompts = {
  
  /**
   * 🔥 系统提示词 - 极简版本，专注速度
   */
  systemPrompts: {
    chinese: {
      interviewer: `你是专业面试官。规则：
1. 回答简洁，不超过30字
2. 直接提问，无寒暄
3. 基于候选人回答追问
4. 保持专业友好语调
5. 纯文本输出，禁用markdown

示例：
候选人："我有3年Java开发经验"
你："能详细说说你在Java项目中遇到的最大技术挑战吗？"`,

      evaluator: `你是面试评估专家。规则：
1. 评价简洁，不超过50字
2. 重点关注关键词匹配
3. 给出1-10分评分
4. 指出优缺点
5. JSON格式输出

示例输出：
{"score": 7, "strengths": ["经验丰富"], "improvements": ["缺少具体案例"]}`
    },

    english: {
      interviewer: `Professional interviewer. Rules:
1. Keep responses under 20 words
2. Ask directly, no small talk
3. Follow up based on answers
4. Professional and friendly tone
5. Plain text only, no markdown

Example:
Candidate: "I have 3 years Java experience"
You: "What was the biggest technical challenge in your Java projects?"`,

      evaluator: `Interview evaluation expert. Rules:
1. Keep evaluation under 30 words
2. Focus on keyword matching
3. Score 1-10
4. Note strengths/improvements
5. JSON format output

Example:
{"score": 7, "strengths": ["experienced"], "improvements": ["needs examples"]}`
    }
  },

  /**
   * 🔥 问题生成模板 - 针对不同岗位优化
   */
  questionTemplates: {
    opening: {
      chinese: "请简单介绍一下你自己和你的技术背景。",
      english: "Please briefly introduce yourself and your technical background."
    },

    technical: {
      frontend: {
        chinese: [
          "描述一个你优化前端性能的具体案例。",
          "你如何处理浏览器兼容性问题？",
          "解释一下你对React/Vue生命周期的理解。"
        ],
        english: [
          "Describe a specific case where you optimized frontend performance.",
          "How do you handle browser compatibility issues?",
          "Explain your understanding of React/Vue lifecycle."
        ]
      },

      backend: {
        chinese: [
          "描述一个你设计的API接口和数据库优化案例。",
          "你如何处理高并发场景下的数据一致性？",
          "解释一下你对微服务架构的理解和实践。"
        ],
        english: [
          "Describe an API design and database optimization case.",
          "How do you handle data consistency in high concurrency?",
          "Explain your understanding and practice of microservices."
        ]
      },

      fullstack: {
        chinese: [
          "描述一个你独立完成的全栈项目。",
          "你如何在前后端之间设计数据交互？",
          "解释一下你对系统架构设计的思考。"
        ],
        english: [
          "Describe a full-stack project you completed independently.",
          "How do you design data interaction between frontend and backend?",
          "Explain your thoughts on system architecture design."
        ]
      }
    },

    behavioral: {
      chinese: [
        "描述一个你在团队中解决冲突的经历。",
        "你如何平衡技术债务和新功能开发？",
        "说说你最近学习的新技术和应用场景。"
      ],
      english: [
        "Describe an experience resolving team conflicts.",
        "How do you balance technical debt and new feature development?",
        "Tell me about a new technology you recently learned."
      ]
    },

    situational: {
      chinese: [
        "如果项目时间紧迫，你会如何保证代码质量？",
        "遇到技术难题时，你的解决思路是什么？",
        "如何向非技术人员解释复杂的技术概念？"
      ],
      english: [
        "How would you ensure code quality under tight deadlines?",
        "What's your approach when facing technical challenges?",
        "How do you explain complex technical concepts to non-technical people?"
      ]
    }
  },

  /**
   * 🔥 缓存键生成规则
   */
  cacheKeyPatterns: {
    question: "llm:q:{position}:{index}:{lang}",
    evaluation: "llm:eval:{questionId}:{answerHash}",
    opening: "llm:opening:{position}:{lang}"
  },

  /**
   * 🔥 性能优化参数
   */
  performanceConfig: {
    maxTokens: {
      question: 100,      // 问题生成限制100 tokens
      evaluation: 200,    // 评估限制200 tokens
      opening: 50         // 开场问题限制50 tokens
    },
    
    temperature: {
      question: 0.2,      // 低温度提升速度和一致性
      evaluation: 0.1,    // 评估需要更高一致性
      opening: 0.3        // 开场问题可以稍微灵活
    },

    cacheExpiry: {
      question: 3600,     // 问题缓存1小时
      evaluation: 1800,   // 评估缓存30分钟
      opening: 7200       // 开场问题缓存2小时
    }
  },

  /**
   * 🔥 错误处理和重试配置
   */
  errorHandling: {
    retryableErrors: [429, 500, 502, 503, 504],
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 8000,
    
    fallbackResponses: {
      chinese: {
        question: "请详细描述一下你在这个领域的工作经验。",
        evaluation: "回答基本符合要求，建议提供更多具体案例。"
      },
      english: {
        question: "Please describe your work experience in this field in detail.",
        evaluation: "Answer meets basic requirements, suggest providing more specific examples."
      }
    }
  },

  /**
   * 🔥 监控和指标配置
   */
  monitoring: {
    performanceThresholds: {
      firstTokenMs: 2000,    // 首个token应在2秒内
      totalTimeMs: 10000,    // 总时间应在10秒内
      cacheHitRate: 0.3      // 缓存命中率应超过30%
    },
    
    alertConditions: {
      consecutiveFailures: 3,
      avgResponseTimeMs: 15000,
      cacheHitRateBelow: 0.1
    }
  }
};

/**
 * 🔥 根据岗位和语言获取优化的系统提示词
 */
export function getOptimizedSystemPrompt(
  role: 'interviewer' | 'evaluator',
  language: 'chinese' | 'english'
): string {
  return OptimizedPrompts.systemPrompts[language][role];
}

/**
 * 🔥 根据岗位类型获取技术问题模板
 */
export function getTechnicalQuestions(
  positionType: 'frontend' | 'backend' | 'fullstack',
  language: 'chinese' | 'english'
): string[] {
  return OptimizedPrompts.questionTemplates.technical[positionType][language];
}

/**
 * 🔥 生成缓存键
 */
export function generateCacheKey(
  type: 'question' | 'evaluation' | 'opening',
  params: Record<string, string>
): string {
  const pattern = OptimizedPrompts.cacheKeyPatterns[type];
  return Object.entries(params).reduce(
    (key, [param, value]) => key.replace(`{${param}}`, value),
    pattern
  );
}

/**
 * 🔥 获取性能配置
 */
export function getPerformanceConfig(type: 'question' | 'evaluation' | 'opening') {
  return {
    maxTokens: OptimizedPrompts.performanceConfig.maxTokens[type],
    temperature: OptimizedPrompts.performanceConfig.temperature[type],
    cacheExpiry: OptimizedPrompts.performanceConfig.cacheExpiry[type]
  };
}
