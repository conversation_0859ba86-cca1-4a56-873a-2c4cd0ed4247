import Redis from 'ioredis';

export class RedisService {
  private static instance: RedisService;
  private redis: Redis;

  private constructor() {
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
      password: process.env.REDIS_PASSWORD || undefined,
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    });

    this.redis.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    this.redis.on('connect', () => {
      console.log('Redis connected successfully');
    });
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  public getClient(): Redis {
    return this.redis;
  }

  // 设置键值对，带过期时间（秒）
  public async set(key: string, value: string, expireInSeconds?: number): Promise<void> {
    if (expireInSeconds) {
      await this.redis.setex(key, expireInSeconds, value);
    } else {
      await this.redis.set(key, value);
    }
  }

  // 获取值
  public async get(key: string): Promise<string | null> {
    return await this.redis.get(key);
  }

  // 删除键
  public async del(key: string): Promise<number> {
    return await this.redis.del(key);
  }

  // 检查键是否存在
  public async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  // 设置键的过期时间
  public async expire(key: string, seconds: number): Promise<boolean> {
    const result = await this.redis.expire(key, seconds);
    return result === 1;
  }

  // 获取键的剩余过期时间
  public async ttl(key: string): Promise<number> {
    return await this.redis.ttl(key);
  }

  // 原子性递增
  public async incr(key: string): Promise<number> {
    return await this.redis.incr(key);
  }

  // 关闭连接
  public async disconnect(): Promise<void> {
    await this.redis.disconnect();
  }
}

export default RedisService;
