import React from 'react';
import { useNavigate } from 'react-router-dom';
import type { InterviewType } from '@new-mianshijun/common';
import { Spark<PERSON>, Brain } from 'lucide-react';
import useAuthStore from '../stores/authStore';
import { useBalance } from '../hooks/useBalance';

interface InterviewCardProps {
  type: InterviewType;
  title: string;
  subtitle: string;
  description: string;
}

const InterviewCard: React.FC<InterviewCardProps> = ({
  type,
  title,
  subtitle,
  description
}) => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { checkCachedBalanceForLogging } = useBalance();
  const isSimulation = type === 'simulation';
  const Icon = isSimulation ? Sparkles : Brain;

  const handleStartExperience = () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // 第一层：缓存检查余额（仅用于日志记录，不阻拦用户）
    const interviewType = isSimulation ? 'mock' : 'formal';
    checkCachedBalanceForLogging(interviewType);

    // 🔥 两种面试类型都直接跳转，保持逻辑一致
    if (isSimulation) {
      // AI模拟面试，跳转到配置页面
      navigate('/interview/mock/config', {
        state: {
          interviewType: 'simulation',
          positionId: null,
          language: 'chinese',
          answerStyle: 'detailed'
        }
      });
    } else {
      // AI正式面试，直接跳转到面试页面
      navigate('/ai-interview');
    }
  };

  return (
    <div className={`relative overflow-hidden rounded-xl ${
      isSimulation
        ? 'bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500'
        : 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500'
    }`}>
      <div className="absolute top-0 right-0 w-32 h-32 transform translate-x-16 -translate-y-8">
        <div className="absolute inset-0 bg-white opacity-10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative p-4">
        <div className="flex items-start gap-3 mb-3">
          <span className="p-2 bg-white bg-opacity-10 rounded-lg">
            <Icon className="w-5 h-5 text-white" />
          </span>
          <div>
            <h3 className="text-lg font-bold text-white">{title}</h3>
            <p className="text-sm text-white text-opacity-80 uppercase tracking-wider">{subtitle}</p>
          </div>
        </div>

        <p className="text-white text-opacity-90 text-sm mb-4">
          {description}
        </p>

        <div className="flex items-center gap-3">
          <button
            onClick={handleStartExperience}
            className="px-4 py-2 bg-white rounded-lg text-sm font-medium text-gray-800 transition-all hover:shadow-lg"
          >
            开始体验
          </button>
          <span className="text-white text-opacity-60 text-xs">
            {isSimulation ? '免费体验' : '消耗 1 次机会'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default InterviewCard;