import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import type { InterviewType } from '@new-mianshijun/common';
import { <PERSON>rk<PERSON>, <PERSON> } from 'lucide-react';
import useAuthStore from '../stores/authStore';
import { useBalance } from '../hooks/useBalance';
import { useToastContext } from '../contexts/ToastContext';
import { CreditsManager } from '../managers/CreditsManager';

interface InterviewCardProps {
  type: InterviewType;
  title: string;
  subtitle: string;
  description: string;
}

const InterviewCard: React.FC<InterviewCardProps> = ({
  type,
  title,
  subtitle,
  description
}) => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { checkCachedBalanceForLogging } = useBalance();
  const { showError } = useToastContext();
  const [isChecking, setIsChecking] = useState(false);
  const isSimulation = type === 'simulation';
  const Icon = isSimulation ? Sparkles : Brain;

  // 🔥 创建CreditsManager实例用于正式面试扣费
  const [creditsManager] = useState(() => new CreditsManager());

  const handleStartExperience = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // 防止重复点击
    if (isChecking) return;

    setIsChecking(true);

    try {
      // 第一层：缓存检查余额（仅用于日志记录，不阻拦用户）
      const interviewType = isSimulation ? 'mock' : 'formal';
      checkCachedBalanceForLogging(interviewType);

      if (isSimulation) {
        // AI模拟面试，直接跳转到配置页面（保持原有逻辑）
        navigate('/interview/mock/config', {
          state: {
            interviewType: 'simulation',
            positionId: null,
            language: 'chinese',
            answerStyle: 'detailed'
          }
        });
      } else {
        // 🔥 AI正式面试，先进行同步扣费验证
        await handleStartFormalInterview();
      }
    } catch (error) {
      console.error('Navigation failed:', error);
      showError('系统繁忙，请稍后重试');
    } finally {
      setIsChecking(false);
    }
  };

  /**
   * 🔥 处理AI正式面试启动 - 同步扣费验证
   */
  const handleStartFormalInterview = async () => {
    try {
      console.log('🔍 Starting formal interview with sync deduction...');

      // 🔥 核心改动：在跳转前，调用同步扣费方法
      await creditsManager.performSyncDeduction('formal');

      // 扣费成功后才跳转
      console.log('✅ Formal interview deduction successful, navigating...');
      navigate('/ai-interview');

    } catch (error: any) {
      // 扣费失败，停留在原页面，并给出错误提示
      console.error('❌ Formal interview deduction failed:', error);

      // 处理不同类型的错误
      let errorMessage = '系统繁忙，请稍后重试';
      if (error.message && error.message.includes('INSUFFICIENT_CREDITS')) {
        errorMessage = error.message.replace('INSUFFICIENT_CREDITS:', '');
      } else if (error.message && error.message.includes('面试机会已用完')) {
        errorMessage = error.message;
      }

      showError(errorMessage);
    }
  };

  return (
    <div className={`relative overflow-hidden rounded-xl ${
      isSimulation
        ? 'bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500'
        : 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500'
    }`}>
      <div className="absolute top-0 right-0 w-32 h-32 transform translate-x-16 -translate-y-8">
        <div className="absolute inset-0 bg-white opacity-10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative p-4">
        <div className="flex items-start gap-3 mb-3">
          <span className="p-2 bg-white bg-opacity-10 rounded-lg">
            <Icon className="w-5 h-5 text-white" />
          </span>
          <div>
            <h3 className="text-lg font-bold text-white">{title}</h3>
            <p className="text-sm text-white text-opacity-80 uppercase tracking-wider">{subtitle}</p>
          </div>
        </div>

        <p className="text-white text-opacity-90 text-sm mb-4">
          {description}
        </p>

        <div className="flex items-center gap-3">
          <button
            onClick={handleStartExperience}
            disabled={isChecking}
            className="px-4 py-2 bg-white rounded-lg text-sm font-medium text-gray-800 transition-all hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isChecking ? '检查中...' : '开始体验'}
          </button>
          <span className="text-white text-opacity-60 text-xs">
            {isSimulation ? '免费体验' : '消耗 1 次机会'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default InterviewCard;